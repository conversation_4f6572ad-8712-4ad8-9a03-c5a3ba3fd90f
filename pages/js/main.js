// 主应用类
class DataMapApp {
    constructor() {
        this.init();
    }

    async init() {
        // 显示加载动画
        this.showLoading();
        
        // 初始化各个组件
        await this.initComponents();
        
        // 隐藏加载动画
        this.hideLoading();
        
        // 启动动画效果
        this.startAnimations();
    }

    async initComponents() {
        // 初始化粒子背景
        // this.initParticles();

        // 初始化统计数据
        // this.initStatistics();

        // 初始化数据平台导航
        this.initPlatforms();

        // 初始化资产分类导航
        this.initAssetCategories();

        // 初始化业务域
        this.initDomains();

        // 初始化数据血缘关系图
        this.initDataLineage();

        // 初始化热门数据资产
        this.initPopularAssets();

        // 初始化数据质量
        this.initDataQuality();

        // 初始化搜索功能
        // this.initSearch();

        // 初始化模态框
        this.initModal();

        // 模拟加载延迟
        await new Promise(resolve => setTimeout(resolve, 1500));
    }

    // 高端粒子背景动画
    initParticles() {
        const container = document.getElementById('particles-container');
        const particleCount = 80; // 增加粒子数量

        for (let i = 0; i < particleCount; i++) {
            setTimeout(() => {
                this.createParticle(container);
            }, i * 75);
        }

        // 持续创建粒子 - 更频繁
        setInterval(() => {
            this.createParticle(container);
        }, 1200);
        
        // 创建特殊发光粒子
        setInterval(() => {
            this.createGlowParticle(container);
        }, 3000);
    }

    createParticle(container) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        
        // 随机位置和大小
        const size = Math.random() * 4 + 2; // 2-6px
        particle.style.width = size + 'px';
        particle.style.height = size + 'px';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDuration = (Math.random() * 5 + 6) + 's';
        particle.style.animationDelay = Math.random() * 3 + 's';
        
        // 高端渐变色彩
        const colors = [
            'linear-gradient(135deg, #667eea, #764ba2)',
            'linear-gradient(135deg, #f093fb, #f5576c)',
            'linear-gradient(135deg, #4facfe, #00f2fe)',
            'linear-gradient(135deg, #43e97b, #38f9d7)',
            'linear-gradient(135deg, #fa709a, #fee140)',
            'linear-gradient(135deg, #a8edea, #fed6e3)',
            'linear-gradient(135deg, #ffecd2, #fcb69f)'
        ];
        particle.style.background = colors[Math.floor(Math.random() * colors.length)];
        
        // 添加发光效果
        particle.style.boxShadow = `0 0 ${size * 4}px rgba(255, 255, 255, 0.3)`;
        
        container.appendChild(particle);
        
        // 动画结束后移除粒子
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 12000);
    }

    // 创建特殊发光粒子
    createGlowParticle(container) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        
        // 更大的发光粒子
        const size = Math.random() * 8 + 6; // 6-14px
        particle.style.width = size + 'px';
        particle.style.height = size + 'px';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDuration = (Math.random() * 8 + 10) + 's';
        particle.style.animationDelay = Math.random() * 2 + 's';
        
        // 特殊发光效果
        particle.style.background = 'radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(102,126,234,0.6) 50%, transparent 100%)';
        particle.style.boxShadow = `
            0 0 ${size * 3}px rgba(102, 126, 234, 0.6),
            0 0 ${size * 6}px rgba(118, 75, 162, 0.4),
            0 0 ${size * 9}px rgba(240, 147, 251, 0.2)
        `;
        particle.style.animation += ', pulse 3s ease-in-out infinite';
        
        container.appendChild(particle);
        
        // 动画结束后移除粒子
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 20000);
    }

    // 初始化统计数据
    initStatistics() {
        const totalAssets = document.getElementById('total-assets');
        const totalDomains = document.getElementById('total-domains');
        const totalApis = document.getElementById('total-apis');
        const totalReports = document.getElementById('total-reports');

        this.animateNumber(totalAssets, DATA_CONFIG.statistics.totalAssets);
        this.animateNumber(totalDomains, DATA_CONFIG.statistics.totalDomains);
        this.animateNumber(totalApis, DATA_CONFIG.statistics.totalApis);
        this.animateNumber(totalReports, DATA_CONFIG.statistics.totalReports);
    }

    // 数字动画 - 改进版本，支持更灵活的动画时长
    animateNumber(element, target, duration = 4000, delay = 0) {
        // 如果目标值是字符串（如 "7000"），转换为数字
        const targetNum = typeof target === 'string' ? parseInt(target.replace(/,/g, '')) : target;

        setTimeout(() => {
            let current = 0;
            const startTime = Date.now();
            const endTime = startTime + duration;

            const updateNumber = () => {
                const now = Date.now();
                const progress = Math.min((now - startTime) / duration, 1);

                // 使用缓动函数让动画更自然
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                current = targetNum * easeOutQuart;

                // 格式化数字显示
                if (targetNum >= 1000) {
                    element.textContent = Math.floor(current).toLocaleString();
                } else {
                    element.textContent = Math.floor(current);
                }

                if (progress < 1) {
                    requestAnimationFrame(updateNumber);
                } else {
                    // 确保最终显示准确的目标值
                    if (targetNum >= 1000) {
                        element.textContent = targetNum.toLocaleString();
                    } else {
                        element.textContent = targetNum;
                    }
                }
            };

            requestAnimationFrame(updateNumber);
        }, delay);
    }

    // 初始化数据平台导航
    initPlatforms() {
        // 为平台导航创建一个新的section，插入到资产分类之前
        const mainContent = document.querySelector('.main-content');
        const assetCategoriesSection = document.querySelector('.asset-categories');

        const platformsSection = document.createElement('section');
        platformsSection.className = 'platforms-overview';
        platformsSection.innerHTML = `
            <h2 class="section-title">
                <i class="fas fa-server"></i>
                数据域概览
            </h2>
            <div class="platforms-grid" id="platforms-grid">
                <!-- 动态生成平台卡片 -->
            </div>
        `;

        mainContent.insertBefore(platformsSection, assetCategoriesSection);

        const container = document.getElementById('platforms-grid');

        DATA_CONFIG.platforms.forEach((platform, index) => {
            const item = this.createPlatformItem(platform, index);
            container.appendChild(item);

            // 延迟显示动画
            setTimeout(() => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(-20px)';
                item.style.transition = 'all 0.4s ease';

                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, 50);
            }, index * 80);
        });
    }

    createPlatformItem(platform, index = 0) {
        const item = document.createElement('div');
        item.className = 'platform-item';
        item.style.setProperty('--platform-color', platform.color);
        item.innerHTML = `
            <div class="platform-icon">
                <i class="${platform.icon}"></i>
            </div>
            <div class="platform-name">${platform.name}</div>
            <div class="platform-count"><span class="platform-count-number">0</span> 个资产</div>
            <div class="platform-type">${this.getPlatformTypeText(platform.type)}</div>
        `;

        item.addEventListener('click', () => {
            // 移除其他活跃状态
            document.querySelectorAll('.platform-item').forEach(el => el.classList.remove('active'));
            // 添加活跃状态
            item.classList.add('active');
            // 筛选显示对应平台的资产
            this.filterAssetsByPlatform(platform.id);
        });

        // 为数字添加动画效果
        const countElement = item.querySelector('.platform-count-number');
        // 延迟启动动画，让页面先渲染完成，每个平台有不同的延迟
        setTimeout(() => {
            this.animateNumber(countElement, platform.count, 4000, 0);
        }, 300 + index * 100);

        return item;
    }

    getPlatformTypeText(type) {
        const typeMap = {
            'database': '数据库',
            'report': '报表平台',
            'bi': 'BI平台',
            'integration': '数据集成',
            'application': '应用平台'
        };
        return typeMap[type] || type;
    }

    filterAssetsByPlatform(platformId) {
        // 这里可以实现按平台筛选资产的逻辑
        console.log('筛选平台资产:', platformId);
    }

    // 初始化资产分类导航
    initAssetCategories() {
        const container = document.getElementById('categories-nav');

        DATA_CONFIG.assetCategories.forEach((category, index) => {
            const item = this.createCategoryItem(category, index);
            container.appendChild(item);

            // 延迟显示动画
            setTimeout(() => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(-20px)';
                item.style.transition = 'all 0.4s ease';

                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, 50);
            }, index * 80);
        });
    }

    createCategoryItem(category, index = 0) {
        const item = document.createElement('div');
        item.className = 'category-item';
        item.style.setProperty('--category-color', category.color);
        item.innerHTML = `
            <div class="category-icon">
                <i class="${category.icon}"></i>
            </div>
            <div class="category-name">${category.name}</div>
            <div class="category-count"><span class="count-number">0</span> 个资产</div>
        `;

        item.addEventListener('click', () => {
            // 移除其他活跃状态
            document.querySelectorAll('.category-item').forEach(el => el.classList.remove('active'));
            // 添加活跃状态
            item.classList.add('active');
            // 筛选显示对应类型的资产
            this.filterAssetsByCategory(category.id);
        });

        // 为数字添加动画效果
        const countElement = item.querySelector('.count-number');
        // 延迟启动动画，让页面先渲染完成，每个分类有不同的延迟
        setTimeout(() => {
            this.animateNumber(countElement, category.count, 4000, 0);
        }, 500 + index * 100);

        return item;
    }

    filterAssetsByCategory(categoryId) {
        // 这里可以实现按分类筛选资产的逻辑
        console.log('筛选资产分类:', categoryId);
    }

    // 初始化业务域
    initDomains() {
        const container = document.getElementById('domains-grid');

        DATA_CONFIG.domains.forEach((domain, index) => {
            const card = this.createDomainCard(domain, index);
            container.appendChild(card);

            // 延迟显示动画
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.5s ease';

                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            }, index * 100);
        });
    }

    createDomainCard(domain, index = 0) {
        const card = document.createElement('div');
        card.className = 'domain-card';
        card.style.setProperty('--domain-color', domain.color);
        card.innerHTML = `
            <div class="domain-header">
                <h3 class="domain-name">${domain.name}</h3>
                <i class="${domain.icon} domain-icon"></i>
            </div>
            <div class="domain-stats">
                <div class="domain-stat">
                    <span class="domain-stat-number asset-count">0</span>
                    <span class="domain-stat-label">数据资产</span>
                </div>
                <div class="domain-stat">
                    <span class="domain-stat-number api-count">0</span>
                    <span class="domain-stat-label">API接口</span>
                </div>
                <div class="domain-stat">
                    <span class="domain-stat-number report-count">0</span>
                    <span class="domain-stat-label">报表</span>
                </div>
            </div>
            <p class="domain-description">${domain.description}</p>
            <div class="domain-asset-types">
                <div class="asset-type-tags">
                    ${domain.assetTypes.map(type => `<span class="asset-type-tag">${type}</span>`).join('')}
                </div>
            </div>
        `;

        card.addEventListener('click', () => {
            this.showDomainDetail(domain);
        });

        // 为统计数字添加动画效果
        const assetCountElement = card.querySelector('.asset-count');
        const apiCountElement = card.querySelector('.api-count');
        const reportCountElement = card.querySelector('.report-count');

        // 延迟启动动画，让页面先渲染完成，每个域卡片有不同的基础延迟
        const baseDelay = 800 + index * 150;
        setTimeout(() => {
            this.animateNumber(assetCountElement, domain.assetCount, 4000, 0);
            this.animateNumber(apiCountElement, domain.apiCount, 4000, 200);
            this.animateNumber(reportCountElement, domain.reportCount, 4000, 400);
        }, baseDelay);

        return card;
    }

    // 初始化数据血缘关系图
    initDataLineage() {
        const container = document.querySelector('.lineage-container');
        const { nodes, links } = DATA_CONFIG.dataLineage;

        // 清空容器并创建新的现代化布局
        container.innerHTML = `
            <div class="lineage-header">
                <div class="header-content">
                    <h3>数据血缘关系图</h3>
                    <p>数据从源头到终端的完整流向</p>
                </div>
                <div class="header-actions">
                    <button class="view-toggle" onclick="app.toggleLineageView()">
                        <span>切换视图</span>
                    </button>
                </div>
            </div>
            <div class="lineage-diagram">
                <svg id="lineage-svg" width="100%" height="400">
                    <!-- 连接线将在这里绘制 -->
                </svg>
                <div class="lineage-nodes" id="lineage-nodes">
                    <!-- 节点将在这里创建 -->
                </div>
            </div>
        `;

        // 定义节点位置（模拟5层架构布局）
        const nodePositions = this.calculateNodePositions(nodes);
        
        // 创建SVG连接线
        this.createDirectConnections(links, nodePositions);
        
        // 创建节点
        this.createNodes(nodes, nodePositions);
        
        // 启动动画效果
        setTimeout(() => {
            this.initDirectFlowAnimations();
        }, 500);
    }

    calculateNodePositions(nodes) {
        const positions = {};
        const containerWidth = 1200; // 适中的容器宽度
        const containerHeight = 360;
        const margin = 60; // 适中的边距
        
        // 节点分层配置（5层水平布局）
        const layers = {
            'source': { nodes: ['mysql', 'postgresql', 'jiandaoyun'], x: margin },
            'integration': { nodes: ['finedatalink'], x: margin + 220 },
            'warehouse': { nodes: ['datawarehouse', 'datamart'], x: margin + 440 },
            'application': { nodes: ['finereport', 'finebi', 'api_services'], x: margin + 660 },
            'consumption': { nodes: ['business_users', 'external_apps'], x: margin + 880 }
        };

        // 计算每层节点位置
        Object.entries(layers).forEach(([layerName, layerConfig]) => {
            const layerNodes = nodes.filter(node => layerConfig.nodes.includes(node.id));
            const nodeCount = layerNodes.length;
            const verticalSpacing = 100; // 进一步增加垂直间距
            const totalHeight = (nodeCount - 1) * verticalSpacing;
            const startY = Math.max(30, (containerHeight - totalHeight) / 2);
            
            layerNodes.forEach((node, index) => {
                positions[node.id] = {
                    x: layerConfig.x,
                    y: startY + index * verticalSpacing,
                    node: node
                };
            });
        });

        return positions;
    }

    createDirectConnections(links, nodePositions) {
        const svg = document.getElementById('lineage-svg');
        svg.innerHTML = '';

        const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');

        // New, more elegant arrowhead
        const arrowMarker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
        arrowMarker.setAttribute('id', 'arrowhead-elegant');
        arrowMarker.setAttribute('viewBox', '0 0 10 10');
        arrowMarker.setAttribute('refX', '9');
        arrowMarker.setAttribute('refY', '5');
        arrowMarker.setAttribute('markerWidth', '6');
        arrowMarker.setAttribute('markerHeight', '6');
        arrowMarker.setAttribute('orient', 'auto-start-reverse');
        
        const arrowPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        arrowPath.setAttribute('d', 'M 0 0 L 10 5 L 0 10 z');
        arrowPath.setAttribute('class', 'arrow-path');
        arrowMarker.appendChild(arrowPath);
        defs.appendChild(arrowMarker);
        
        svg.appendChild(defs);

        links.forEach((link, index) => {
            const sourcePos = nodePositions[link.source];
            const targetPos = nodePositions[link.target];
            
            if (sourcePos && targetPos) {
                this.createConnectionLine(svg, sourcePos, targetPos, link, index);
            }
        });
    }

    createConnectionLine(svg, sourcePos, targetPos, link, index) {
        const nodeWidth = 120;
        const nodeHeight = 70;
        const containerPadding = 20;

        let sx = sourcePos.x + nodeWidth + containerPadding;
        let sy = sourcePos.y + (nodeHeight / 2) + containerPadding;
        let ex = targetPos.x + containerPadding;
        let ey = targetPos.y + (nodeHeight / 2) + containerPadding;
        
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        const pathData = this.getElegantCurvePath(sx, sy, ex, ey);
        path.setAttribute('d', pathData);

        path.setAttribute('class', 'connection-line');
        path.setAttribute('marker-end', 'url(#arrowhead-elegant)');
        
        svg.appendChild(path);
        const pathLength = path.getTotalLength();
        svg.removeChild(path);

        const group = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        group.setAttribute('class', 'connection-group');
        group.setAttribute('data-source', link.source);
        group.setAttribute('data-target', link.target);
        group.appendChild(path);

        // 添加粒子流动效果
        const particlePath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        particlePath.setAttribute('d', pathData);
        particlePath.setAttribute('class', 'particle-flow');
        particlePath.setAttribute('stroke-dasharray', '10,5');
        group.appendChild(particlePath);

        // 添加粒子点
        for (let i = 0; i < 3; i++) {
            const particleDot = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            particleDot.setAttribute('r', '3');
            particleDot.setAttribute('class', 'particle-dot');
            particleDot.style.animationDelay = `${i * 1}s`;

            // 使用animateMotion让粒子沿路径移动
            const animateMotion = document.createElementNS('http://www.w3.org/2000/svg', 'animateMotion');
            animateMotion.setAttribute('dur', '3s');
            animateMotion.setAttribute('repeatCount', 'indefinite');
            animateMotion.setAttribute('begin', `${i * 1}s`);

            const mpath = document.createElementNS('http://www.w3.org/2000/svg', 'mpath');
            mpath.setAttributeNS('http://www.w3.org/1999/xlink', 'href', `#path-${index}`);
            animateMotion.appendChild(mpath);
            particleDot.appendChild(animateMotion);

            group.appendChild(particleDot);
        }

        // 给主路径添加ID以供粒子引用
        path.setAttribute('id', `path-${index}`);

        if (link.label) {
            const labelPosition = pathLength / 2;
            const labelPoint = path.getPointAtLength(labelPosition);
            
            const foreignObject = document.createElementNS('http://www.w3.org/2000/svg', 'foreignObject');
            const labelDiv = document.createElement('div');
            labelDiv.setAttribute('xmlns', 'http://www.w3.org/1999/xhtml');
            
            labelDiv.className = 'line-label-capsule';
            labelDiv.textContent = link.label;

            foreignObject.appendChild(labelDiv);
            group.appendChild(foreignObject);

            const textWidth = link.label.length * 7 + 20;
            const textHeight = 22;
            foreignObject.setAttribute('x', labelPoint.x - textWidth / 2);
            foreignObject.setAttribute('y', labelPoint.y - textHeight / 2);
            foreignObject.setAttribute('width', textWidth);
            foreignObject.setAttribute('height', textHeight);
        }

        group.style.opacity = '0';
        group.style.animation = `fadeIn 0.6s ease-out ${index * 0.05}s forwards`;
        
        svg.appendChild(group);
    }

    getElegantCurvePath(startX, startY, endX, endY) {
        const dx = endX - startX;
        const curveX = dx * 0.55;
        return `M ${startX} ${startY} C ${startX + curveX} ${startY}, ${endX - curveX} ${endY}, ${endX} ${endY}`;
    }

    createNodes(nodes, nodePositions) {
        const nodesContainer = document.getElementById('lineage-nodes');
        
        Object.entries(nodePositions).forEach(([nodeId, position], index) => {
            const node = position.node;
            const nodeElement = this.createDirectNodeCard(node, position);
            nodesContainer.appendChild(nodeElement);
            
            setTimeout(() => {
                nodeElement.style.opacity = '1';
                nodeElement.style.transform = 'translateY(0) scale(1)';
            }, index * 50 + 200); // Stagger animation
        });
    }

    createDirectNodeCard(node, position) {
        const card = document.createElement('div');
        card.className = 'lineage-node-direct';
        card.setAttribute('data-node-id', node.id);
        card.setAttribute('data-node-type', node.type);
        card.style.left = position.x + 'px';
        card.style.top = position.y + 'px';
        
        const nodeConfig = this.getNodeConfig(node.type);
        
        card.innerHTML = `
            <div class="node-content">
                <div class="node-icon" style="background: ${nodeConfig.color}">
                    ${nodeConfig.icon}
                </div>
                <div class="node-title">
                    <h4>${node.name.replace('\\n', ' ')}</h4>
                </div>
            </div>
        `;

        card.addEventListener('click', () => {
            this.showNodeDetails(node.id);
        });

        card.addEventListener('mouseenter', () => {
            this.highlightDirectConnections(node.id, true);
            card.classList.add('node-active');
        });

        card.addEventListener('mouseleave', () => {
            this.highlightDirectConnections(node.id, false);
            card.classList.remove('node-active');
        });

        card.style.opacity = '0';
        card.style.transform = 'translateY(20px) scale(0.9)';
        card.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';

        return card;
    }

    highlightDirectConnections(nodeId, highlight) {
        document.querySelectorAll('.lineage-node-direct').forEach(nodeElement => {
            const otherNodeId = nodeElement.getAttribute('data-node-id');
            if (otherNodeId === nodeId) return;

            if (this.areNodesConnected(nodeId, otherNodeId)) {
                if (highlight) nodeElement.classList.add('node-connected');
                else nodeElement.classList.remove('node-connected');
            } else {
                if (highlight) nodeElement.classList.add('node-dimmed');
                else nodeElement.classList.remove('node-dimmed');
            }
        });
        
        document.querySelectorAll('.connection-group').forEach(group => {
            const source = group.getAttribute('data-source');
            const target = group.getAttribute('data-target');
            
            if (source === nodeId || target === nodeId) {
                if (highlight) group.classList.add('active');
                else group.classList.remove('active');
            } else {
                if (highlight) group.classList.add('dimmed');
                else group.classList.remove('dimmed');
            }
        });
    }

    areNodesConnected(nodeId1, nodeId2) {
        const { links } = DATA_CONFIG.dataLineage;
        return links.some(link => 
            (link.source === nodeId1 && link.target === nodeId2) ||
            (link.source === nodeId2 && link.target === nodeId1)
        );
    }

    initDirectFlowAnimations() {
        // 启动连续粒子流动效果
        this.startContinuousParticleFlow();

        // 随机激活连接线的强化效果
        setInterval(() => {
            this.randomConnectionPulse();
        }, 3000);
    }

    startContinuousParticleFlow() {
        const connections = document.querySelectorAll('.connection-group');

        connections.forEach((connection, index) => {
            // 为每个连接添加随机延迟的连续流动效果
            setTimeout(() => {
                connection.classList.add('flowing');

                // 随机改变粒子颜色
                setInterval(() => {
                    this.randomizeParticleColor(connection);
                }, 5000 + Math.random() * 3000);

            }, index * 500 + Math.random() * 1000);
        });
    }

    randomizeParticleColor(connection) {
        const colors = ['#00ff88', '#4a90e2', '#ff6b6b', '#fdcb6e', '#fd79a8', '#a29bfe'];
        const randomColor = colors[Math.floor(Math.random() * colors.length)];

        const particleFlow = connection.querySelector('.particle-flow');
        const particleDots = connection.querySelectorAll('.particle-dot');

        if (particleFlow) {
            particleFlow.style.stroke = randomColor;
            particleFlow.style.filter = `drop-shadow(0 0 4px ${randomColor})`;
        }

        particleDots.forEach(dot => {
            dot.style.fill = randomColor;
            dot.style.filter = `drop-shadow(0 0 6px ${randomColor})`;
        });
    }

    randomConnectionPulse() {
        const connections = document.querySelectorAll('.connection-group');
        if (connections.length > 0) {
            const randomConnection = connections[Math.floor(Math.random() * connections.length)];

            // 临时增强粒子流动效果
            randomConnection.classList.add('active');

            const particleFlow = randomConnection.querySelector('.particle-flow');
            const particleDots = randomConnection.querySelectorAll('.particle-dot');

            if (particleFlow) {
                // 临时增强粒子流
                particleFlow.style.strokeWidth = '3';
                particleFlow.style.opacity = '1';
                particleFlow.style.stroke = '#00ff00';
                particleFlow.style.filter = 'drop-shadow(0 0 8px #00ff00)';

                particleDots.forEach(dot => {
                    dot.style.fill = '#00ff00';
                    dot.style.filter = 'drop-shadow(0 0 10px #00ff00)';
                    dot.setAttribute('r', '4');
                });

                setTimeout(() => {
                    randomConnection.classList.remove('active');
                    particleFlow.style.strokeWidth = '2';
                    particleFlow.style.opacity = '0.8';
                    this.randomizeParticleColor(randomConnection);

                    particleDots.forEach(dot => {
                        dot.setAttribute('r', '3');
                    });
                }, 1500);
            }
        }
    }

    toggleLineageView() {
        const diagram = document.querySelector('.lineage-diagram');
        diagram.classList.toggle('compact-view');
    }

    // 初始化热门数据资产
    initPopularAssets() {
        const container = document.getElementById('assets-grid');

        DATA_CONFIG.popularAssets.forEach((asset, index) => {
            const card = this.createAssetCard(asset);
            container.appendChild(card);

            // 延迟显示动画
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateX(-30px)';
                card.style.transition = 'all 0.5s ease';

                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateX(0)';
                }, 100);
            }, index * 150);
        });
    }

    createAssetCard(asset) {
        const card = document.createElement('div');
        card.className = 'asset-card';

        card.innerHTML = `
            <div class="asset-header">
                <i class="${asset.icon} asset-icon"></i>
                <span class="asset-type">${asset.type}</span>
            </div>
            <div class="asset-name">${asset.name}</div>
            <div class="asset-platform">
                <i class="fas fa-server"></i>
                <span>${asset.platform}</span>
            </div>
            <div class="asset-description">${asset.description}</div>
            <div class="asset-meta">
                <div>
                    <span class="asset-domain">${asset.domain}</span>
                    <span class="asset-usage ${asset.usage}">${this.getUsageText(asset.usage)}</span>
                </div>
                <div>
                    <small>记录: ${asset.recordCount} | 更新: ${asset.lastUpdate}</small>
                </div>
            </div>
        `;

        card.addEventListener('click', () => {
            this.showAssetDetail(asset);
        });

        return card;
    }

    getUsageText(usage) {
        const usageMap = {
            'high': '高频使用',
            'medium': '中频使用',
            'low': '低频使用'
        };
        return usageMap[usage] || usage;
    }

    // 初始化数据质量
    initDataQuality() {
        const progressBars = document.querySelectorAll('.progress-fill');
        
        progressBars.forEach((bar, index) => {
            const progress = bar.getAttribute('data-progress');
            setTimeout(() => {
                bar.style.setProperty('--progress', progress + '%');
            }, index * 200);
        });
    }

    // 初始化搜索功能
    initSearch() {
        const searchInput = document.getElementById('search-input');
        let searchTimeout;
        
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.performSearch(e.target.value);
            }, 300);
        });
        
        searchInput.addEventListener('focus', () => {
            this.showSearchSuggestions();
        });
    }

    performSearch(query) {
        if (!query.trim()) {
            this.hideSearchResults();
            return;
        }

        // 模拟搜索结果
        const results = this.searchData(query);
        this.showSearchResults(results);
    }

    searchData(query) {
        const results = [];
        const lowerQuery = query.toLowerCase();

        // 搜索业务域
        DATA_CONFIG.domains.forEach(domain => {
            if (domain.name.toLowerCase().includes(lowerQuery) ||
                domain.description.toLowerCase().includes(lowerQuery)) {
                results.push({
                    type: 'domain',
                    name: domain.name,
                    description: domain.description,
                    data: domain
                });
            }
        });

        // 搜索数据资产
        DATA_CONFIG.popularAssets.forEach(asset => {
            if (asset.name.toLowerCase().includes(lowerQuery) ||
                asset.description.toLowerCase().includes(lowerQuery) ||
                asset.type.toLowerCase().includes(lowerQuery)) {
                results.push({
                    type: 'asset',
                    name: asset.name,
                    description: asset.description,
                    data: asset
                });
            }
        });

        return results;
    }

    showSearchResults(results) {
        let suggestionsContainer = document.querySelector('.search-suggestions');

        if (!suggestionsContainer) {
            suggestionsContainer = document.createElement('div');
            suggestionsContainer.className = 'search-suggestions';
            document.querySelector('.search-container').appendChild(suggestionsContainer);
        }

        if (results.length === 0) {
            suggestionsContainer.innerHTML = '<div class="suggestion-item">未找到相关结果</div>';
        } else {
            suggestionsContainer.innerHTML = results.map(result => `
                <div class="suggestion-item" data-type="${result.type}" data-name="${result.name}">
                    <strong>${result.name}</strong>
                    <br>
                    <small>${result.description}</small>
                </div>
            `).join('');

            // 添加点击事件
            suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
                item.addEventListener('click', () => {
                    const type = item.getAttribute('data-type');
                    const name = item.getAttribute('data-name');
                    const data = results.find(r => r.name === name).data;

                    if (type === 'domain') {
                        this.showDomainDetail(data);
                    } else if (type === 'asset') {
                        this.showAssetDetail(data);
                    }

                    this.hideSearchResults();
                });
            });
        }

        suggestionsContainer.style.display = 'block';
    }

    hideSearchResults() {
        const suggestionsContainer = document.querySelector('.search-suggestions');
        if (suggestionsContainer) {
            suggestionsContainer.style.display = 'none';
        }
    }

    showSearchSuggestions() {
        const suggestions = SEARCH_CONFIG.suggestions;
        let suggestionsContainer = document.querySelector('.search-suggestions');

        if (!suggestionsContainer) {
            suggestionsContainer = document.createElement('div');
            suggestionsContainer.className = 'search-suggestions';
            document.querySelector('.search-container').appendChild(suggestionsContainer);
        }

        suggestionsContainer.innerHTML = suggestions.map(suggestion => `
            <div class="suggestion-item">${suggestion}</div>
        `).join('');

        // 添加点击事件
        suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                document.getElementById('search-input').value = item.textContent;
                this.performSearch(item.textContent);
            });
        });

        suggestionsContainer.style.display = 'block';
    }

    // 初始化模态框
    initModal() {
        const modal = document.getElementById('detail-modal');
        const closeBtn = modal.querySelector('.close');
        
        closeBtn.addEventListener('click', () => {
            this.hideModal();
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hideModal();
            }
        });
    }

    // 显示域详情
    showDomainDetail(domain) {
        const modalBody = document.getElementById('modal-body');
        modalBody.innerHTML = `
            <h2><i class="${domain.icon}"></i> ${domain.name}</h2>
            <p>${domain.description}</p>
            <div class="domain-detail-stats">
                <div class="stat-item">
                    <span class="stat-number">${domain.assetCount}</span>
                    <span class="stat-label">数据资产</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${domain.apiCount}</span>
                    <span class="stat-label">API接口</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">${domain.reportCount}</span>
                    <span class="stat-label">报表</span>
                </div>
            </div>
            <h3>子域包含:</h3>
            <ul>
                ${domain.subDomains.map(sub => `<li>${sub}</li>`).join('')}
            </ul>
            <h3>资产类型:</h3>
            <div class="asset-type-tags">
                ${domain.assetTypes.map(type => `<span class="asset-type-tag">${type}</span>`).join('')}
            </div>
        `;
        this.showModal();
    }

    // 显示资产详情
    showAssetDetail(asset) {
        const modalBody = document.getElementById('modal-body');
        modalBody.innerHTML = `
            <h2><i class="${asset.icon}"></i> ${asset.name}</h2>
            <div class="asset-detail-header">
                <span class="asset-type-badge">${asset.type}</span>
                <span class="asset-domain-badge">${asset.domain}</span>
                <span class="asset-platform-badge">${asset.platform}</span>
            </div>
            <p><strong>描述:</strong> ${asset.description}</p>
            <div class="asset-detail-stats">
                <div class="stat-item">
                    <span class="stat-label">数据平台</span>
                    <span class="stat-number">${asset.platform}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">记录数/状态</span>
                    <span class="stat-number">${asset.recordCount}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">最后更新</span>
                    <span class="stat-number">${asset.lastUpdate}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">使用频率</span>
                    <span class="stat-number usage-${asset.usage}">${this.getUsageText(asset.usage)}</span>
                </div>
            </div>
            ${this.getAssetTypeSpecificInfo(asset)}
        `;
        this.showModal();
    }

    getAssetTypeSpecificInfo(asset) {
        switch(asset.type) {
            case 'MySQL表':
                return `
                    <h3>MySQL表信息:</h3>
                    <p>• 存储引擎: InnoDB</p>
                    <p>• 字符集: UTF8MB4</p>
                    <p>• 索引策略: 主键+业务索引</p>
                    <p>• 备份策略: 每日全量备份</p>
                `;
            case 'PostgreSQL表':
                return `
                    <h3>PostgreSQL表信息:</h3>
                    <p>• 表空间: 默认表空间</p>
                    <p>• 分区策略: 按时间分区</p>
                    <p>• 索引类型: B-tree索引</p>
                    <p>• 统计信息: 自动更新</p>
                `;
            case 'FineReport报表':
                return `
                    <h3>FineReport报表信息:</h3>
                    <p>• 报表类型: 分页报表</p>
                    <p>• 数据源: 多数据源</p>
                    <p>• 导出格式: PDF, Excel, Word</p>
                    <p>• 刷新策略: 定时刷新</p>
                `;
            case 'FineBI仪表板':
            case 'FineBI分析':
                return `
                    <h3>FineBI分析信息:</h3>
                    <p>• 组件数量: 6-12个</p>
                    <p>• 数据更新: 实时/定时</p>
                    <p>• 交互功能: 钻取、联动、筛选</p>
                    <p>• 移动端: 支持响应式</p>
                `;
            case 'FineDataLink管道':
                return `
                    <h3>FineDataLink管道信息:</h3>
                    <p>• 任务类型: ETL数据处理</p>
                    <p>• 调度频率: 每小时执行</p>
                    <p>• 数据源: MySQL, PostgreSQL</p>
                    <p>• 目标: 数据仓库</p>
                `;
            case '简道云应用':
                return `
                    <h3>简道云应用信息:</h3>
                    <p>• 应用类型: 业务流程应用</p>
                    <p>• 表单数量: 15个</p>
                    <p>• 流程节点: 审批、通知</p>
                    <p>• 权限控制: 角色权限</p>
                `;
            default:
                return `
                    <h3>资产详细信息:</h3>
                    <p>• 平台: ${asset.platform}</p>
                    <p>• 类型: ${asset.type}</p>
                    <p>• 状态: 正常运行</p>
                `;
        }
    }

    showModal() {
        const modal = document.getElementById('detail-modal');
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }

    hideModal() {
        const modal = document.getElementById('detail-modal');
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    // 显示加载动画
    showLoading() {
        const loading = document.getElementById('loading-overlay');
        loading.style.display = 'flex';
    }

    // 隐藏加载动画
    hideLoading() {
        const loading = document.getElementById('loading-overlay');
        loading.style.opacity = '0';
        setTimeout(() => {
            loading.style.display = 'none';
        }, 500);
    }

    // 启动高端动画效果
    startAnimations() {
        // 为所有卡片添加精致的延迟动画
        document.querySelectorAll('.domain-card, .asset-card, .platform-item, .category-item, .quality-card').forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px) scale(0.95)';
            card.style.filter = 'blur(5px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0) scale(1)';
                card.style.filter = 'blur(0px)';
            }, index * 120);
        });

        // 为section标题添加动画
        document.querySelectorAll('.section-title').forEach((title, index) => {
            title.style.opacity = '0';
            title.style.transform = 'translateX(-30px)';
            
            setTimeout(() => {
                title.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                title.style.opacity = '1';
                title.style.transform = 'translateX(0)';
            }, index * 200);
        });

        // 为血缘关系图添加特殊动画
        setTimeout(() => {
            const lineageContainer = document.querySelector('.lineage-container');
            if (lineageContainer) {
                lineageContainer.style.opacity = '0';
                lineageContainer.style.transform = 'scale(0.9)';
                lineageContainer.style.transition = 'all 1s cubic-bezier(0.4, 0, 0.2, 1)';
                
                setTimeout(() => {
                    lineageContainer.style.opacity = '1';
                    lineageContainer.style.transform = 'scale(1)';
                }, 100);
            }
        }, 500);

        this.startHeaderAnimation();
        this.startScrollAnimations();
    }

    startHeaderAnimation() {
        const logo = document.querySelector('.logo i');
        // 更柔和的logo动画
        setInterval(() => {
            logo.style.transform = 'scale(1.1) rotate(10deg)';
            setTimeout(() => {
                logo.style.transform = 'scale(1) rotate(0deg)';
            }, 800);
        }, 8000);
    }

    startScrollAnimations() {
        // 高级滚动动画观察器
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    
                    // 为质量卡片添加特殊效果
                    if (entry.target.classList.contains('quality-card')) {
                        setTimeout(() => {
                            const progressBars = entry.target.querySelectorAll('.progress-fill');
                            progressBars.forEach(bar => {
                                const progress = bar.getAttribute('data-progress');
                                bar.style.setProperty('--progress', progress + '%');
                            });
                        }, 300);
                    }
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // 观察所有需要动画的元素
        document.querySelectorAll('.domain-card, .asset-card, .quality-card, .lineage-container').forEach(el => {
            observer.observe(el);
        });
    }

    getNodeConfig(nodeType) {
        const configs = {
            'mysql': {
                icon: '🗄️',
                color: 'linear-gradient(135deg, #4285f4, #1a73e8)',
                displayName: 'MySQL数据库',
                description: '业务核心数据存储，提供高可用的事务处理能力'
            },
            'postgresql': {
                icon: '🗃️',
                color: 'linear-gradient(135deg, #336791, #2d5a87)',
                displayName: 'PostgreSQL数据库',
                description: '高性能分析数据库，支持复杂查询和分析'
            },
            'jiandaoyun': {
                icon: '☁️',
                color: 'linear-gradient(135deg, #10d9c4, #0891b2)',
                displayName: '简道云平台',
                description: '低代码业务应用平台，快速构建业务流程'
            },
            'finedatalink': {
                icon: '🔗',
                color: 'linear-gradient(135deg, #ff6b6b, #dc2626)',
                displayName: 'FineDataLink',
                description: '企业级数据集成平台，支持多源数据同步'
            },
            'datawarehouse': {
                icon: '🏭',
                color: 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
                displayName: '数据仓库',
                description: '企业级数据仓库，ODS/DWD/DWS分层架构'
            },
            'datamart': {
                icon: '📊',
                color: 'linear-gradient(135deg, #a855f7, #9333ea)',
                displayName: '数据集市',
                description: '面向业务的主题数据集市，ADS应用数据服务'
            },
            'finereport': {
                icon: '📈',
                color: 'linear-gradient(135deg, #f59e0b, #d97706)',
                displayName: 'FineReport',
                description: '企业级报表平台，丰富的图表和报表功能'
            },
            'finebi': {
                icon: '📊',
                color: 'linear-gradient(135deg, #10b981, #059669)',
                displayName: 'FineBI',
                description: '自助式商业智能平台，敏捷BI分析'
            },
            'api_services': {
                icon: '🔌',
                color: 'linear-gradient(135deg, #3b82f6, #2563eb)',
                displayName: 'API服务',
                description: '数据API服务接口，支持实时数据访问'
            },
            'business_users': {
                icon: '👥',
                color: 'linear-gradient(135deg, #6366f1, #4f46e5)',
                displayName: '业务用户',
                description: '企业业务用户，进行数据分析和决策支持'
            },
            'external_apps': {
                icon: '🌐',
                color: 'linear-gradient(135deg, #06b6d4, #0891b2)',
                displayName: '外部系统',
                description: '第三方系统集成，扩展数据应用场景'
            }
        };
        
        return configs[nodeType] || configs['mysql'];
    }

    showNodeDetails(nodeId) {
        const { nodes } = DATA_CONFIG.dataLineage;
        const node = nodes.find(n => n.id === nodeId);
        if (node) {
            const config = this.getNodeConfig(node.type);
            
            document.getElementById('modal-body').innerHTML = `
                <div class="node-detail-modal">
                    <div class="node-detail-header">
                        <div class="node-detail-icon" style="background: ${config.color}">
                            ${config.icon}
                        </div>
                        <div class="node-detail-title">
                            <h2>${node.name.replace('\n', ' ')}</h2>
                            <p>${config.displayName}</p>
                        </div>
                    </div>
                    <div class="node-detail-content">
                        <div class="detail-section">
                            <h3>系统描述</h3>
                            <p>${config.description}</p>
                        </div>
                        <div class="detail-section">
                            <h3>连接信息</h3>
                            <div class="connection-list">
                                ${this.getConnectionDetails(nodeId)}
                            </div>
                        </div>
                        <div class="detail-section">
                            <h3>运行状态</h3>
                            <div class="status-indicators">
                                <div class="status-item">
                                    <span class="status-dot active"></span>
                                    <span>服务正常</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-dot active"></span>
                                    <span>数据同步</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            this.showModal();
        }
    }

    getConnectionDetails(nodeId) {
        const { links } = DATA_CONFIG.dataLineage;
        const connections = links.filter(link => 
            link.source === nodeId || link.target === nodeId
        );
        
        if (connections.length === 0) {
            return '<p class="no-connections">暂无连接</p>';
        }
        
        return connections.map(link => `
            <div class="connection-item">
                <div class="connection-type">
                    ${link.source === nodeId ? '输出到' : '输入自'}
                </div>
                <div class="connection-label">${link.label}</div>
            </div>
        `).join('');
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new DataMapApp();
});

// 添加一些全局样式类
const style = document.createElement('style');
style.textContent = `
    .animate-in {
        animation: slideInUp 0.6s ease-out;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .high-usage { border-left: 3px solid #ff6b6b; }
    .medium-usage { border-left: 3px solid #f7b731; }
    .low-usage { border-left: 3px solid #4ecdc4; }
`;
document.head.appendChild(style);
